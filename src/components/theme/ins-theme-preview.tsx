'use client';

import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export function InsThemePreview() {
  return (
    <div className="p-8 space-y-8 bg-background min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold bg-gradient-to-r from-ins-mint-600 to-ins-coral-500 bg-clip-text text-transparent mb-2">
          INS风格主题预览
        </h1>
        <p className="text-muted-foreground mb-8">
          舒适清新的Instagram风格色调，高级感满满
        </p>

        {/* 色彩展示 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="p-6 space-y-4">
            <h3 className="font-semibold text-ins-mint-700">薄荷绿系列</h3>
            <div className="space-y-2">
              <div className="h-8 bg-ins-mint-100 rounded flex items-center px-3 text-xs">ins-mint-100</div>
              <div className="h-8 bg-ins-mint-300 rounded flex items-center px-3 text-xs">ins-mint-300</div>
              <div className="h-8 bg-ins-mint-500 rounded flex items-center px-3 text-xs text-white">ins-mint-500</div>
              <div className="h-8 bg-ins-mint-700 rounded flex items-center px-3 text-xs text-white">ins-mint-700</div>
            </div>
          </Card>

          <Card className="p-6 space-y-4">
            <h3 className="font-semibold text-ins-coral-700">珊瑚橙系列</h3>
            <div className="space-y-2">
              <div className="h-8 bg-ins-coral-100 rounded flex items-center px-3 text-xs">ins-coral-100</div>
              <div className="h-8 bg-ins-coral-300 rounded flex items-center px-3 text-xs">ins-coral-300</div>
              <div className="h-8 bg-ins-coral-500 rounded flex items-center px-3 text-xs text-white">ins-coral-500</div>
              <div className="h-8 bg-ins-coral-700 rounded flex items-center px-3 text-xs text-white">ins-coral-700</div>
            </div>
          </Card>

          <Card className="p-6 space-y-4">
            <h3 className="font-semibold text-ins-pink-700">淡粉色系列</h3>
            <div className="space-y-2">
              <div className="h-8 bg-ins-pink-100 rounded flex items-center px-3 text-xs">ins-pink-100</div>
              <div className="h-8 bg-ins-pink-300 rounded flex items-center px-3 text-xs">ins-pink-300</div>
              <div className="h-8 bg-ins-pink-500 rounded flex items-center px-3 text-xs text-white">ins-pink-500</div>
              <div className="h-8 bg-ins-pink-700 rounded flex items-center px-3 text-xs text-white">ins-pink-700</div>
            </div>
          </Card>

          <Card className="p-6 space-y-4">
            <h3 className="font-semibold text-ins-warm-700">温暖色系列</h3>
            <div className="space-y-2">
              <div className="h-8 bg-ins-warm-100 rounded flex items-center px-3 text-xs">ins-warm-100</div>
              <div className="h-8 bg-ins-warm-300 rounded flex items-center px-3 text-xs">ins-warm-300</div>
              <div className="h-8 bg-ins-warm-500 rounded flex items-center px-3 text-xs text-white">ins-warm-500</div>
              <div className="h-8 bg-ins-warm-700 rounded flex items-center px-3 text-xs text-white">ins-warm-700</div>
            </div>
          </Card>
        </div>

        {/* 组件展示 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <Card className="p-6 space-y-4">
            <h3 className="font-semibold">按钮样式</h3>
            <div className="space-y-3">
              <Button className="w-full bg-gradient-to-r from-ins-mint-500 to-ins-coral-500 text-white">
                主要按钮
              </Button>
              <Button variant="secondary" className="w-full">
                次要按钮
              </Button>
              <Button variant="outline" className="w-full">
                边框按钮
              </Button>
            </div>
          </Card>

          <Card className="p-6 space-y-4">
            <h3 className="font-semibold">渐变效果</h3>
            <div className="space-y-3">
              <div className="h-12 bg-gradient-to-r from-ins-mint-200 to-ins-coral-200 rounded flex items-center justify-center">
                浅色渐变
              </div>
              <div className="h-12 bg-gradient-to-r from-ins-mint-500 to-ins-coral-500 rounded flex items-center justify-center text-white">
                中等渐变
              </div>
              <div className="h-12 bg-gradient-to-r from-ins-mint-700 to-ins-coral-700 rounded flex items-center justify-center text-white">
                深色渐变
              </div>
            </div>
          </Card>
        </div>

        {/* 背景效果展示 */}
        <Card className="p-6 space-y-4">
          <h3 className="font-semibold">背景效果</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="h-32 bg-radial-glow-mint rounded flex items-center justify-center">
              薄荷绿光晕
            </div>
            <div className="h-32 bg-radial-glow-coral rounded flex items-center justify-center">
              珊瑚色光晕
            </div>
            <div className="h-32 glass-card flex items-center justify-center">
              玻璃效果
            </div>
          </div>
        </Card>

        {/* 文字效果展示 */}
        <Card className="p-6 space-y-4">
          <h3 className="font-semibold">文字效果</h3>
          <div className="space-y-4">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-ins-mint-600 to-ins-coral-500 bg-clip-text text-transparent">
              大标题渐变效果
            </h1>
            <h2 className="text-2xl font-semibold text-ins-mint-600">
              薄荷绿标题
            </h2>
            <h3 className="text-xl font-medium text-ins-coral-500">
              珊瑚色标题
            </h3>
            <p className="text-muted-foreground">
              这是一段普通的文字，展示了新主题的可读性和舒适度。
            </p>
          </div>
        </Card>
      </div>
    </div>
  );
}

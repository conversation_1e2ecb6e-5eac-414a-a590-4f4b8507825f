'use client';

import { cn } from "@/lib/utils";

interface GradientHoverProps {
  children: React.ReactNode;
  className?: string;
  from?: string;
  via?: string;
  to?: string;
  hoverFrom?: string;
  hoverVia?: string;
  hoverTo?: string;
}

export function GradientHover({
  children,
  className,
  from = "ins-mint-600",
  via = "ins-coral-600",
  to = "ins-mint-600",
  hoverFrom = "ins-coral-600",
  hoverVia = "ins-mint-600",
  hoverTo = "ins-coral-600",
}: GradientHoverProps) {
  return (
    <span
      className={cn(
        "bg-gradient-to-r bg-clip-text text-transparent transition-all duration-700 bg-[length:200%_auto] hover:bg-[position:100%_0] cursor-pointer",
        `from-${from} via-${via} to-${to}`,
        `hover:from-${hoverFrom} hover:via-${hoverVia} hover:to-${hoverTo}`,
        className
      )}
    >
      {children}
    </span>
  );
} 
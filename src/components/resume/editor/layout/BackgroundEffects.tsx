import { cn } from "@/lib/utils";

interface BackgroundEffectsProps {
  className?: string;
  isBaseResume?: boolean;
}

export function BackgroundEffects({ className, isBaseResume = true }: BackgroundEffectsProps) {
  return (
    <div className={cn("fixed inset-0 z-0  overflow-hidden  h-[calc(100vh)]", className)}>
      {/* Base Gradient */}
      <div className={cn(
        "absolute inset-0 bg-gradient-to-br",
        isBaseResume
          ? "from-ins-warm-50/50 via-ins-mint-50/50 to-ins-coral-50/50"
          : "from-sky-100/80 via-blue-50/80 to-sky-100/80"
      )} />
      
      {/* Grid Pattern */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#8882_1px,transparent_1px),linear-gradient(to_bottom,#8882_1px,transparent_1px)] bg-[size:14px_24px]" />
      
      {/* Animated Gradient Orbs */}
      <div 
        className={cn(
          "absolute top-1/4 left-1/4 w-96 h-96 rounded-full blur-3xl animate-float-slow",
          isBaseResume
            ? "bg-gradient-to-br from-ins-mint-200/20 to-ins-coral-200/20"
            : "bg-gradient-to-br from-sky-300/30 to-blue-300/30"
        )}
        style={{ willChange: 'transform' }}
      />
      <div 
        className={cn(
          "absolute bottom-1/4 right-1/4 w-96 h-96 rounded-full blur-3xl animate-float-slower",
          isBaseResume
            ? "bg-gradient-to-br from-ins-coral-200/20 to-ins-pink-200/20"
            : "bg-gradient-to-br from-blue-300/30 to-sky-300/30"
        )}
        style={{ willChange: 'transform' }}
      />
    </div>
  );
} 
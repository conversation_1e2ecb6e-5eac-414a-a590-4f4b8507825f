import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Bo<PERSON>, Star, Briefcase, FileSearch } from "lucide-react";

interface QuickSuggestionsProps {
  onSuggestionClick: (suggestion: string) => void;
}

const suggestions = [
  {
    text: "给我的简历打分（满分 10 分）",
    icon: Star,
  },
  {
    text: "优化工作经历部分",
    icon: Briefcase,
  },
  {
    text: "评价我的简历",
    icon: FileSearch,
  },
];

export function QuickSuggestions({ onSuggestionClick }: QuickSuggestionsProps) {
  return (
    <div className="flex flex-col items-center gap-4 py-6">
      <div className="flex items-center gap-2">
        <Bot className="h-4 w-4 text-ins-mint-500" />
        <p className="text-sm text-ins-mint-500">
          尝试以下建议
        </p>
      </div>

      <div className="flex flex-wrap justify-center gap-2 max-w-[600px]">
        {suggestions.map((suggestion) => {
          const Icon = suggestion.icon;
          return (
            <Button
              key={suggestion.text}
              variant="ghost"
              onClick={() => onSuggestionClick(suggestion.text)}
              className={cn(
                "h-9 px-3",
                "bg-white/40",
                "text-ins-mint-700 text-sm",
                "border border-ins-mint-100",
                "hover:bg-ins-mint-50/60 hover:border-ins-mint-200 hover:text-ins-mint-800",
                "transition-colors"
              )}
            >
              <Icon className="mr-2 h-3.5 w-3.5" />
              {suggestion.text}
            </Button>
          );
        })}
      </div>
    </div>
  );
} 
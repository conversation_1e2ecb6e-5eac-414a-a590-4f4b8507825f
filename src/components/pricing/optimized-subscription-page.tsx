'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Check, 
  Clock, 
  Users, 
  TrendingUp, 
  Shield, 
  Crown,
  Star,
  Zap,
  ArrowRight
} from 'lucide-react';
import { createPortalSession } from '@/app/(dashboard)/subscription/stripe-session';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface Profile {
  subscription_plan: string | null;
  subscription_status: string | null;
  current_period_end: string | null;
  trial_end: string | null;
  stripe_customer_id: string | null;
  stripe_subscription_id: string | null;
}

interface OptimizedSubscriptionPageProps {
  initialProfile: Profile | null;
}

const testimonials = [
  {
    name: "<PERSON>",
    role: "Google 软件工程师",
    content: "ZHIXIN 帮助我在第一周就获得了3个面试机会。AI 建议非常准确。",
    avatar: "SC"
  },
  {
    name: "<PERSON>",
    role: "Meta 产品经理",
    content: "回复率从2%提升到15%。这个工具在我第一次面试时就回本了。",
    avatar: "M<PERSON>"
  },
  {
    name: "<PERSON>",
    role: "Microsoft 数据科学家",
    content: "定制简历功能改变了游戏规则。3周内就找到了理想工作。",
    avatar: "ER"
  }
];

export function OptimizedSubscriptionPage({ initialProfile }: OptimizedSubscriptionPageProps) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  
  const isPro = initialProfile?.subscription_plan?.toLowerCase() === 'pro';
  const isCanceling = initialProfile?.subscription_status === 'canceled';

  const handleUpgrade = async () => {
    if (isPro) {
      // Handle portal session for existing pro users
      try {
        setIsLoading(true);
        const result = await createPortalSession();
        if (result?.url) {
          window.location.href = result.url;
        }
      } catch {
        // Handle error silently
      } finally {
        setIsLoading(false);
      }
    } else {
      // Handle checkout for free users
      const priceId = process.env.NEXT_PUBLIC_STRIPE_PRO_PRICE_ID;
      if (priceId) {
        router.push(`/subscription/checkout?price_id=${priceId}`);
      }
    }
  };

  // Calculate days remaining for canceling users
  const daysRemaining = initialProfile?.current_period_end
    ? Math.max(0, Math.ceil((new Date(initialProfile.current_period_end).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)))
    : 0;

  const endDate = initialProfile?.current_period_end 
    ? new Date(initialProfile.current_period_end).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long', 
        day: 'numeric'
      })
    : null;

  return (
    <div className="min-h-screen bg-gradient-to-br from-ins-warm-50 via-ins-mint-50/30 to-ins-coral-50/50 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-24 -right-24 w-96 h-96 bg-gradient-to-br from-ins-mint-400/10 to-ins-coral-600/10 rounded-full blur-3xl" />
        <div className="absolute -bottom-24 -left-24 w-96 h-96 bg-gradient-to-br from-ins-coral-400/10 to-ins-pink-600/10 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 container mx-auto px-4 py-8 max-w-6xl pb-24">
        {/* Header Section - State Aware */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-4"
        >
          {isCanceling ? (
            <>
              <div className="flex items-center justify-center mb-4">
                <Clock className="h-8 w-8 text-amber-500 mr-3" />
                <Badge variant="outline" className="text-amber-700 border-amber-300 bg-amber-50">
                  剩余 {daysRemaining} 天
                </Badge>
              </div>
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                不要失去您的竞争优势
              </h1>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                您的专业版访问将于 {endDate} 结束。保持势头，继续获得面试机会。
              </p>
            </>
          ) : isPro ? (
            <>
              <div className="flex items-center justify-center mb-4">
                <Crown className="h-8 w-8 text-ins-mint-500 mr-3" />
                <Badge className="bg-ins-mint-100 text-ins-mint-700 border-ins-mint-300">
                  专业版会员
                </Badge>
              </div>
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                您正在最大化职业潜力
              </h1>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                继续利用高级 AI 工具在求职中保持领先。
              </p>
            </>
          ) : (
            <>
              <div className="flex items-center justify-center mb-4">
                <TrendingUp className="h-8 w-8 text-blue-500 mr-3" />
                <Badge variant="outline" className="text-blue-700 border-blue-300 bg-blue-50">
                  面试率提升3倍
                </Badge>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                准备好获得理想工作了吗？
              </h1>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                加入数千名通过 ZHIXIN 专业版提升职业生涯的专业人士。
              </p>
            </>
          )}
        </motion.div>

        {/* Social Proof Bar */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="flex items-center justify-center mb-8 text-sm text-gray-600"
        >
          <Users className="h-4 w-4 mr-2" />
          <span>受到 12,000+ 专业人士信赖</span>
          <div className="flex ml-4">
            {[...Array(5)].map((_, i) => (
              <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
            ))}
          </div>
          <span className="ml-2">4.9/5 评分</span>
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Left Column - Value Proposition */}
          <motion.div 
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
            className="space-y-8"
          >
            {/* Key Benefits */}
            <div className="space-y-6">
              <h2 className="text-2xl font-semibold text-gray-900">
                {isPro ? "您的专业版权益" : "专业版为您提供"}
              </h2>
              
              <div className="grid gap-4">
                {[
                  {
                    icon: Zap,
                    title: "求职申请速度提升3倍",
                    description: "AI 驱动的定制每周节省15+小时",
                    highlight: true
                  },
                  {
                    icon: TrendingUp,
                    title: "更高回复率",
                    description: "会员面试邀请增加300%",
                    highlight: true
                  },
                  {
                    icon: Crown,
                    title: "无限制使用",
                    description: "简历、定制或 AI 助手无使用限制"
                  }
                ].map((benefit, index) => (
                  <div 
                    key={index}
                    className={cn(
                      "flex items-start space-x-4 p-4 rounded-lg transition-colors",
                      benefit.highlight ? "bg-blue-50 border border-blue-200" : "hover:bg-gray-50"
                    )}
                  >
                    <div className={cn(
                      "p-2 rounded-lg",
                      benefit.highlight ? "bg-blue-100" : "bg-gray-100"
                    )}>
                      <benefit.icon className={cn(
                        "h-5 w-5",
                        benefit.highlight ? "text-blue-600" : "text-gray-600"
                      )} />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">{benefit.title}</h3>
                      <p className="text-sm text-gray-600">{benefit.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Testimonials */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Success Stories</h3>
              <div className="space-y-4">
                {testimonials.slice(0, 2).map((testimonial, index) => (
                  <div key={index} className="p-4 bg-white rounded-lg shadow-sm border border-gray-200">
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-ins-mint-600 rounded-full flex items-center justify-center text-white text-xs font-medium">
                        {testimonial.avatar}
                      </div>
                      <div className="flex-1">
                        <p className="text-sm text-gray-700 mb-1">&ldquo;{testimonial.content}&rdquo;</p>
                        <p className="text-xs text-gray-500">
                          <strong>{testimonial.name}</strong> • {testimonial.role}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Right Column - Pricing & CTA */}
          <motion.div 
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
            className="space-y-6"
          >
            {/* Pricing Section */}
            <div className="bg-white rounded-2xl border border-gray-200 p-8 shadow-lg relative overflow-hidden">
              {!isPro && (
                <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-ins-mint-500 to-ins-coral-600" />
              )}
              
              <div className="text-center mb-6">
                <div className="flex items-center justify-center mb-2">
                  <h3 className="text-2xl font-bold text-gray-900">ZHIXIN Pro</h3>
                  {!isPro && (
                    <Badge className="ml-3 bg-blue-100 text-blue-700">最受欢迎</Badge>
                  )}
                </div>
                
                <div className="mb-4">
                  <span className="text-4xl font-bold text-gray-900">$20</span>
                  <span className="text-gray-600">/月</span>
                </div>
                
                {!isPro && (
                  <div className="space-y-2 text-sm text-gray-600">
                    <p>💰 <strong>一次面试即可回本</strong></p>
                    <p>⏰ 每月费用不到一顿午餐</p>
                    <p>💼 对比：简历写手收费 $260+</p>
                  </div>
                )}
              </div>

              {/* Feature List */}
              <div className="space-y-3 mb-8">
                {[
                  "无限基础简历",
                  "无限 AI 定制简历",
                  "高级 AI 助手",
                  "高级 ATS 优化模板",
                  "优先客户支持",
                  "自定义品牌选项"
                ].map((feature, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <Check className="h-5 w-5 text-green-500 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>

              {/* Risk Reduction */}
              {!isPro && (
                <div className="flex items-center justify-center space-x-4 mb-6 p-3 bg-green-50 rounded-lg border border-green-200">
                  <Shield className="h-5 w-5 text-green-600" />
                  <span className="text-sm text-green-700 font-medium">
                    30天退款保证
                  </span>
                </div>
              )}

              {/* CTA Button */}
              <Button
                onClick={handleUpgrade}
                disabled={isLoading}
                className={cn(
                  "w-full py-6 text-lg font-semibold rounded-xl transition-all duration-300",
                  isPro
                    ? "bg-gray-100 text-gray-700 hover:bg-gray-200"
                    : "bg-gradient-to-r from-ins-mint-600 to-ins-coral-600 hover:from-ins-mint-700 hover:to-ins-coral-700 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                )}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    <span>处理中...</span>
                  </div>
                ) : isPro ? (
                  "管理订阅"
                ) : (
                  <div className="flex items-center justify-center space-x-2">
                    <span>开始获得更多面试机会</span>
                    <ArrowRight className="h-5 w-5" />
                  </div>
                )}
              </Button>

              {!isPro && (
                <p className="text-center text-xs text-gray-500 mt-4">
                  随时取消 • 无隐藏费用 • 即时访问
                </p>
              )}
            </div>

            {/* Additional CTA for canceling users */}
            {isCanceling && (
              <div className="bg-amber-50 border border-amber-200 rounded-xl p-6">
                <div className="text-center">
                  <h4 className="font-semibold text-amber-900 mb-2">限时优惠</h4>
                  <p className="text-sm text-amber-700 mb-4">
                    立即重新激活，享受买一送一优惠
                  </p>
                  <Button
                    onClick={handleUpgrade}
                    className="bg-amber-600 hover:bg-amber-700 text-white"
                  >
                    重新激活并节省50%
                  </Button>
                </div>
              </div>
            )}


          </motion.div>
        </div>
      </div>
    </div>
  );
} 
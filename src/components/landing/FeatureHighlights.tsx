"use client"
import React from 'react';
import Image from "next/image";
import { motion } from "framer-motion";
import { CheckCircle2 } from "lucide-react";
import Link from "next/link";
import { SplitContent } from '../ui/split-content';
import { AuthDialog } from "@/components/auth/auth-dialog";

const FeatureHighlights = () => {
  // Enhanced features with metrics, testimonials, and benefit-focused language


  // Trusted by logos
  const companies = [
    { name: "Google", logo: "/logos/google.png" },
    { name: "Microsoft", logo: "/logos/microsoft.webp" },
    { name: "Amazon", logo: "/logos/amazon.png" },
    { name: "<PERSON><PERSON>", logo: "/logos/meta.png" },
    { name: "Netflix", logo: "/logos/netflix.png" },
  ];

  // Statistics counters
  const stats = [
    { value: "500+", label: "已创建简历" },
    { value: "89%", label: "面试成功率" },
    { value: "4.9/5", label: "用户评分" },
    { value: "15 min", label: "平均设置时间" },
  ];

  // Animation variants for scroll reveal
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  return (
    <section className="py-24 md:py-32 px-4 sm:px-6 relative overflow-hidden">
      {/* Enhanced decorative elements */}
      <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-ins-mint-200/30 to-ins-mint-200/30 blur-3xl"></div>
      <div className="absolute -bottom-40 -left-40 w-96 h-96 rounded-full bg-gradient-to-tr from-teal-200/30 to-cyan-200/30 blur-3xl"></div>
      <div className="absolute top-1/3 left-1/4 w-64 h-64 rounded-full bg-gradient-to-r from-pink-200/20 to-rose-200/20 blur-3xl"></div>
 
      {/* Redesigned heading section with enhanced visual appeal */}
      <div className="relative z-10 max-w-5xl mx-auto">
        {/* Decorative elements specific to the heading */}
        <div className="absolute -top-28 left-1/2 -translate-x-1/2 w-[800px] h-[800px] rounded-full bg-gradient-to-br from-ins-mint-200/15 to-ins-coral-200/15 blur-3xl -z-10"></div>
        <div className="absolute -top-20 -right-20 w-80 h-80 rounded-full bg-gradient-to-br from-cyan-200/20 to-teal-200/20 blur-3xl -z-10"></div>
        <div className="absolute -bottom-10 -left-20 w-72 h-72 rounded-full bg-gradient-to-tr from-rose-200/20 to-pink-200/20 blur-3xl -z-10"></div>
        
        {/* Leading badges - multi-color approach inspired by Hero.tsx */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="flex justify-center gap-3 mb-4"
        >
          <span className="px-3 py-1 rounded-full bg-gradient-to-r from-ins-mint-600/10 to-ins-coral-600/10 border border-ins-mint-200/40 text-sm text-ins-mint-700">
            AI-Powered
          </span>
          <span className="px-3 py-1 rounded-full bg-gradient-to-r from-ins-coral-600/10 to-ins-pink-600/10 border border-ins-coral-200/40 text-sm text-ins-coral-700">
            ATS-Optimized
          </span>
          <span className="px-3 py-1 rounded-full bg-gradient-to-r from-ins-warm-600/10 to-ins-mint-600/10 border border-ins-warm-200/40 text-sm text-ins-warm-700">
            100% Free
          </span>
        </motion.div>
        
        {/* Heading with enhanced typography */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.2 }}
          className="text-center mb-4"
        >
          <h2 className="text-4xl md:text-6xl font-bold leading-tight tracking-tight">
            <span className="inline-block bg-gradient-to-r from-ins-mint-600 to-ins-coral-600 bg-clip-text text-transparent">
              The Resume Builder
            </span>
            <br />
            <motion.span 
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="inline-block mt-1 bg-gradient-to-r from-ins-coral-600 to-ins-pink-600 bg-clip-text text-transparent"
            >
              That Gets You Hired
            </motion.span>
          </h2>
          
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.7 }}
            className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto mt-3"
          >
            Smart AI tools that optimize your resume for each job, increasing your interview chances by up to <span className="font-semibold text-teal-700">3x</span>
          </motion.p>
        </motion.div>

        {/* Enhanced statistics with animated reveal - no cards, just colorful inline stats */}
        <motion.div 
          className="flex flex-wrap justify-center gap-8 md:gap-12 mx-auto mt-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
        >
          {stats.map((stat, index) => {
            const textColors = [
              "text-ins-mint-700",
              "text-ins-coral-700",
              "text-ins-pink-700",
              "text-ins-warm-700"
            ];
            
            return (
              <motion.div 
                key={index}
                variants={itemVariants}
                className="text-center relative"
              >
                <motion.p 
                  initial={{ scale: 0.9, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.1 * index }}
                  className="text-3xl md:text-4xl font-bold"
                >
                  <span className="text-ins-mint-600">
                    {stat.value}
                  </span>
                </motion.p>
                <p className={`text-sm md:text-base ${textColors[index]} mt-1`}>
                  {stat.label}
                </p>
              </motion.div>
            );
          })}
        </motion.div>
        
        {/* Colorful separators */}
        <div className="flex justify-center my-12">
          <div className="w-16 h-[3px] bg-gradient-to-r from-ins-mint-500 to-ins-coral-500 rounded-full mx-1"></div>
          <div className="w-16 h-[3px] bg-gradient-to-r from-ins-coral-500 to-ins-pink-500 rounded-full mx-1"></div>
          <div className="w-16 h-[3px] bg-gradient-to-r from-ins-pink-500 to-ins-warm-500 rounded-full mx-1"></div>
        </div>
      </div>
      
      {/* Enhanced Features Section with improved card styling */}
      <div className="flex flex-col gap-24 py-24 relative" id="features">
            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-ins-mint-50/30 to-transparent"></div>
            
            <SplitContent
              imageSrc="/SS Chat.png"
              heading="AI 智能简历助手"
              description="从我们的高级 AI 助手获得实时反馈和建议。优化您的简历内容，改进要点描述，确保您的技能在招聘人员和 ATS 系统中脱颖而出。"
              imageOnLeft={false}
              imageOverflowRight={true}
              badgeText="要点效果提升90%"
              badgeGradient="from-ins-mint-600/10 to-ins-coral-600/10"
              bulletPoints={[
                "基于您经验的智能内容建议",
                "简历实时反馈",
                "行业特定优化"
              ]}
            />

            <SplitContent
              imageSrc="/Dashboard Image.png"
              heading="精美的简历仪表板"
              description="使用我们直观的仪表板在一个地方管理您的所有简历。创建基础简历，为特定职位生成定制版本，轻松跟踪您的申请进度。"
              imageOnLeft={true}
              badgeText="整理您的求职过程"
              badgeGradient="from-teal-600/10 to-cyan-600/10"
              bulletPoints={[
                "集中式简历管理",
                "所有简历的版本控制",
                "跟踪申请状态"
              ]}
            />

            <SplitContent
              imageSrc="/SS Score.png"
              heading="简历效果评分"
              description="通过我们的综合评分系统获得简历效果的详细洞察。跟踪关键指标，识别改进领域，优化您的简历以在雇主和 ATS 系统中脱颖而出。"
              imageOnLeft={false}
              imageOverflowRight={true}
              badgeText="回复率提升3倍"
              badgeGradient="from-pink-600/10 to-rose-600/10"
              bulletPoints={[
                "ATS 兼容性评分",
                "关键词优化洞察",
                "详细改进建议"
              ]}
            />

            <SplitContent
              imageSrc="/SS Cover Letter.png"
              heading="AI 求职信生成器"
              description="使用我们的 AI 驱动生成器，在几分钟内创建引人注目的个性化求职信。针对特定职位机会定制您的信息，同时保持专业且引人入胜的语调。"
              imageOnLeft={true}
              badgeText="每份申请节省30+分钟"
              badgeGradient="from-emerald-600/10 to-green-600/10"
              bulletPoints={[
                "针对职位要求定制",
                "专业的语调和结构",
                "突出您的相关成就"
              ]}
            />
      </div>
      
      {/* Social proof section - Trusted by companies */}
      <motion.div 
        className="mt-24 text-center"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, margin: "-100px" }}
        transition={{ duration: 0.8 }}
      >
        <h3 className="text-xl text-muted-foreground mb-8">获得来自以下公司专业人士的信赖</h3>
        <div className="flex flex-wrap justify-center items-center gap-8 md:gap-12 max-w-4xl mx-auto opacity-80">
          {companies.map((company, index) => (
            <div key={index} className="w-24 h-12 relative transition-all duration-300">
              <Image 
                src={company.logo} 
                alt={company.name} 
                fill
                className="object-contain" 
                sizes="100px"
              />
            </div>
          ))}
        </div>
      </motion.div>
      
      {/* Enhanced CTA section */}
      <motion.div 
        className="mt-28 text-center"
        initial={{ opacity: 0, scale: 0.95 }}
        whileInView={{ opacity: 1, scale: 1 }}
        viewport={{ once: true, margin: "-100px" }}
        transition={{ duration: 0.5 }}
      >
        <div className="max-w-3xl mx-auto px-6 py-12 rounded-2xl bg-gradient-to-br from-white/50 to-white/30 backdrop-blur-lg border border-white/40 shadow-xl">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-ins-mint-600">
            准备好获得您的理想工作了吗？
          </h2>
          <p className="text-lg text-muted-foreground mb-8">
            加入50,000+位通过 ZHIXIN 获得更多面试机会的专业人士
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <AuthDialog>
              <button 
                className="px-8 py-4 rounded-lg bg-gradient-to-r from-ins-mint-600 to-ins-coral-600 text-white text-lg font-medium shadow-lg transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"
              >
                Create Your Free Resume
              </button>
            </AuthDialog>
            <Link 
              href="https://github.com/olyaiy/resume-lm" 
              target="_blank"
              rel="noopener noreferrer"
              className="px-8 py-4 rounded-lg bg-white/80 border border-ins-mint-200/40 text-lg font-medium transition-all duration-300 hover:-translate-y-1 hover:shadow-lg"
            >
              <span className="bg-gradient-to-r from-ins-mint-600 to-ins-coral-600 bg-clip-text text-transparent">
                Open Source on Github
              </span>
            </Link>
          </div>
          
          <p className="text-sm text-muted-foreground mt-6 flex items-center justify-center gap-2">
            <CheckCircle2 className="w-4 h-4 text-green-500" />
            No credit card required • 100% free
          </p>
        </div>
      </motion.div>

      {/* Sticky mobile CTA - only visible on mobile/tablet */}
      <div className="md:hidden fixed bottom-4 left-0 right-0 z-50 px-4">
        <AuthDialog>
          <button 
            className="flex items-center justify-center w-full py-3.5 rounded-lg bg-gradient-to-r from-ins-mint-600 to-ins-coral-600 text-white font-medium shadow-lg"
          >
            Get Started Now
          </button>
        </AuthDialog>
      </div>
    </section>
  );
};

export default FeatureHighlights;

"use client"

import React, { useRef } from 'react';
import { motion, useInView } from "framer-motion";
import { HelpCircle, Sparkles } from "lucide-react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

interface FAQItem {
  question: string;
  answer: string;
}

export function FAQ() {
  // Refs for intersection observer
  const sectionRef = useRef<HTMLElement>(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });

  // FAQ data - focused on ZHIXIN specific questions
  const faqItems: FAQItem[] = [
    {
      question: "ZHIXIN 的 AI 如何为特定职位定制我的简历？",
      answer: "我们的 AI 分析职位描述，自动调整您的简历内容、关键词和格式，以匹配招聘人员和 ATS 系统的要求。它优化您的要点描述，突出相关技能，确保您的经验与职位要求保持一致。"
    },
    {
      question: "ZHIXIN 真的免费使用吗？",
      answer: "是的！我们的免费计划包括2份基础简历和4份使用 GPT-4.1 Nano 模型的定制简历。您还可以自托管整个平台。我们的专业版（$20/月）提供无限简历和高级 AI 模型访问，如 Claude、GPT-4 和 Gemini。"
    },
    {
      question: "ZHIXIN 与其他简历构建器有什么不同？",
      answer: "ZHIXIN 专为技术专业人士设计，具有 AI 驱动的优化、ATS 兼容性，以及从一份基础简历创建多个定制版本的能力。此外，它是开源的，可以自托管以完全控制您的数据。"
    },
    {
      question: "使用 ZHIXIN 创建简历需要多长时间？",
      answer: "大多数用户在15分钟内创建他们的第一份简历。一旦您有了基础简历，使用我们的 AI 助手为特定职位生成定制版本只需2-3分钟。"
    },
    {
      question: "我的简历能通过 ATS（申请人跟踪系统）吗？",
      answer: "当然可以！ZHIXIN 专门设计用于创建 ATS 优化的简历。我们的模板使用正确的格式、关键词优化和结构，ATS 系统可以轻松解析并给予高排名。"
    },
    {
      question: "有哪些 AI 模型可用？",
      answer: "免费用户可以访问我们的 GPT-4.1 Nano 模型进行基本 AI 功能。专业版订阅者可以访问高级模型，包括 Claude、GPT-4、Gemini 等，以增强简历生成和优化。"
    },
    {
      question: "我的数据安全和隐私吗？",
      answer: "您的隐私是我们的首要任务。所有数据都经过加密，您甚至可以自托管 ZHIXIN 以获得完全控制。我们绝不会与第三方分享您的个人信息或简历数据。"
    },
    {
      question: "你们为学生或转行者提供支持吗？",
      answer: "当然！ZHIXIN 非常适合学生、转行者和任何级别的专业人士。我们的 AI 帮助突出可转移技能，无论您的经验水平如何，都能优化您的简历。"
    }
  ];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.1 }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };

  return (
    <section 
      ref={sectionRef}
      className="py-16 md:py-20 px-4 relative overflow-hidden scroll-mt-20" 
      id="faq"
      aria-labelledby="faq-heading"
    >
      {/* Simplified background decoration */}
      <div aria-hidden="true" className="absolute -top-32 -right-32 w-64 h-64 rounded-full bg-gradient-to-br from-ins-mint-200/15 to-ins-mint-200/15 blur-3xl"></div>
      <div aria-hidden="true" className="absolute -bottom-32 -left-32 w-64 h-64 rounded-full bg-gradient-to-tr from-teal-200/15 to-cyan-200/15 blur-3xl"></div>
      
      {/* Compact Heading Section */}
      <div className="relative z-10 max-w-2xl mx-auto text-center mb-12">
        {/* Badge */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: -20 }}
          transition={{ duration: 0.6 }}
          className="flex justify-center mb-3"
        >
          <span className="px-3 py-1 rounded-full bg-gradient-to-r from-ins-mint-600/10 to-ins-coral-600/10 border border-ins-mint-200/40 text-sm text-ins-mint-700 flex items-center gap-2">
            <HelpCircle className="w-4 h-4" />
            FAQ
          </span>
        </motion.div>
        
        {/* Compact heading */}
        <motion.h2
          id="faq-heading"
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.7, delay: 0.2 }}
          className="text-3xl md:text-4xl font-bold tracking-tight mb-3"
        >
          <span className="bg-gradient-to-r from-ins-mint-600 to-ins-coral-600 bg-clip-text text-transparent">
            Questions & Answers
          </span>
        </motion.h2>
        
        {/* Shorter description */}
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-base md:text-lg text-muted-foreground"
        >
          Quick answers to help you get started with ZHIXIN
        </motion.p>
      </div>
      
      {/* Compact FAQ Accordion */}
      <motion.div
        className="relative z-10 max-w-3xl mx-auto"
        variants={containerVariants}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
      >
        <Accordion type="single" collapsible className="space-y-2">
          {faqItems.map((item, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="group"
            >
              <AccordionItem 
                value={`item-${index}`} 
                className="border border-gray-200/50 rounded-lg bg-white/40 backdrop-blur-sm hover:bg-white/60 transition-all duration-200 hover:shadow-sm hover:border-ins-mint-200/50 px-4 py-1"
              >
                <AccordionTrigger className="text-left hover:no-underline group-hover:text-ins-mint-700 transition-colors duration-200 py-4 text-sm md:text-base font-medium">
                  <span className="flex items-start gap-2">
                    <Sparkles className="w-4 h-4 text-ins-mint-600 mt-0.5 flex-shrink-0 opacity-60 group-hover:opacity-100 transition-opacity duration-200" />
                    {item.question}
                  </span>
                </AccordionTrigger>
                <AccordionContent className="text-muted-foreground leading-relaxed pb-4 pl-6 text-sm">
                  {item.answer}
                </AccordionContent>
              </AccordionItem>
            </motion.div>
          ))}
                </Accordion>
      </motion.div>
    </section>
  );
} 
'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import Link from "next/link";
import { useState, useEffect } from "react";

interface WelcomeDialogProps {
  isOpen: boolean;
}

export function WelcomeDialog({ isOpen: initialIsOpen }: WelcomeDialogProps) {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    setIsOpen(initialIsOpen);
  }, [initialIsOpen]);

  return (
    <Dialog 
      open={isOpen} 
      onOpenChange={setIsOpen}
    >
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-2xl font-semibold bg-gradient-to-r from-teal-600 to-cyan-600 bg-clip-text text-transparent">
            欢迎使用 ZHIXIN！🎉
          </DialogTitle>
        </DialogHeader>
        
        <div className="pt-4 space-y-6">
          <h3 className="font-medium text-foreground">以下是开始使用的方法：</h3>
          <div className="space-y-4">
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-teal-100 to-cyan-100 flex items-center justify-center">
                <span className="text-sm font-semibold bg-gradient-to-br from-teal-600 to-cyan-600 bg-clip-text text-transparent">1</span>
              </div>
              <div className="flex-1 pt-1">
                <p className="text-muted-foreground">填写您的个人资料，包括工作经验、教育背景和技能</p>
              </div>
            </div>
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-ins-mint-100 to-ins-mint-100 flex items-center justify-center">
                <span className="text-sm font-semibold bg-gradient-to-br from-ins-mint-600 to-ins-mint-600 bg-clip-text text-transparent">2</span>
              </div>
              <div className="flex-1 pt-1">
                <p className="text-muted-foreground">为您感兴趣的不同类型职位创建基础简历</p>
              </div>
            </div>
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-sky-100 to-blue-100 flex items-center justify-center">
                <span className="text-sm font-semibold text-sky-600">3</span>
              </div>
              <div className="flex-1 pt-1">
                <p className="text-muted-foreground">使用您的基础简历为特定职位申请创建定制版本</p>
              </div>
            </div>
          </div>
          <div className="pt-2 space-y-2">
            <Link href="/profile">
              <Button className="w-full bg-gradient-to-r from-teal-600 to-cyan-600 text-white">
                开始填写您的个人资料
              </Button>
            </Link>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => setIsOpen(false)}
            >
              稍后再做
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 
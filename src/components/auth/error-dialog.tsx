'use client';

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { AlertCircle } from "lucide-react";
import Link from "next/link";
import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";

interface ErrorDialogProps {
  isOpen: boolean;
}

export function ErrorDialog({ isOpen: initialIsOpen }: ErrorDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const searchParams = useSearchParams();

  useEffect(() => {
    setIsOpen(initialIsOpen);
  }, [initialIsOpen]);

  const errorMessage = isOpen ? (
    searchParams.get('error') === 'auth_code_missing'
      ? '无法完成您的登录。请重试。'
      : '您的邮箱确认出现问题。请检查您的收件箱并重试。'
  ) : null;

  return (
    <Dialog 
      open={isOpen} 
      onOpenChange={setIsOpen}
    >
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="mx-auto rounded-full w-12 h-12 bg-red-100 flex items-center justify-center mb-4">
            <AlertCircle className="w-6 h-6 text-red-600" />
          </div>
          <DialogTitle className="text-center text-2xl font-semibold text-red-600">
            认证错误
          </DialogTitle>
          <DialogDescription>{errorMessage}</DialogDescription>
        </DialogHeader>
        
        <div className="pt-4 space-y-4">
          <p className="text-center text-muted-foreground">
            确认您的邮箱地址时出现错误。可能的原因包括：
          </p>
          <ul className="list-disc list-inside space-y-2 text-muted-foreground">
            <li>确认链接已过期</li>
            <li>链接已被使用</li>
            <li>链接无效</li>
          </ul>
          <div className="pt-4 space-y-2">
            <Link href="/">
              <Button className="w-full bg-gradient-to-r from-red-600 to-rose-600 text-white">
                重新尝试登录
              </Button>
            </Link>
            <Link href="https://x.com/alexfromvan" target="_blank" rel="noopener noreferrer">
              <Button
                variant="outline"
                className="w-full"
              >
                联系客服
              </Button>
            </Link>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 
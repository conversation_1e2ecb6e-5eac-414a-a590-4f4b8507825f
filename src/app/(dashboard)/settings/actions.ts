'use server'

import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";
import OpenAI from "openai";
import { ProxyConfig } from "@/lib/proxy";

interface SecurityResult {
  success: boolean;
  error?: string;
}

export async function updateEmail(formData: FormData): Promise<SecurityResult> {
  const supabase = await createClient();
  const newEmail = formData.get('email') as string;
  const currentPassword = formData.get('currentPassword') as string;

  // First verify the current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  
  if (userError || !user?.email) {
    return { success: false, error: '无法验证当前用户' };
  }

  // Don't update if it's the same email
  if (user.email === newEmail) {
    return { success: false, error: '新邮箱必须与当前邮箱不同' };
  }

  // Verify current password first
  const { error: signInError } = await supabase.auth.signInWithPassword({
    email: user.email,
    password: currentPassword,
  });

  if (signInError) {
    return { success: false, error: '当前密码不正确' };
  }

  // Then update the email
  const { error } = await supabase.auth.updateUser({ email: newEmail });

  if (error) {
    return { success: false, error: error.message };
  }

  revalidatePath('/settings');
  return { success: true };
}

export async function updatePassword(formData: FormData): Promise<SecurityResult> {
  const supabase = await createClient();
  const currentPassword = formData.get('currentPassword') as string;
  const newPassword = formData.get('newPassword') as string;

  // Get the current user's email
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  
  if (userError || !user?.email) {
    return { success: false, error: '无法验证当前用户' };
  }

  // First verify the current password
  const { error: signInError } = await supabase.auth.signInWithPassword({
    email: user.email,
    password: currentPassword,
  });

  if (signInError) {
    return { success: false, error: '当前密码不正确' };
  }

  // Then update to the new password
  const { error } = await supabase.auth.updateUser({ password: newPassword });

  if (error) {
    return { success: false, error: error.message };
  }

  revalidatePath('/settings');
  return { success: true };
} 



interface ApiTestResult {
    success: boolean;
    message?: string;
    error?: string;
  }
  
  export async function testApiKey(): Promise<ApiTestResult> {
    try {
      const supabase = await createClient()
      
      // Get the API key from vault
      const { data: apiKey, error: keyError } = await supabase
        .rpc('get_api_key', {
          p_service_name: 'openai'
        })
  
      if (keyError || !apiKey) {
        return { 
          success: false, 
          error: '未找到 OpenAI API 密钥'
        }
      }
  
      const proxyConfig = ProxyConfig.getFetchConfig();
      const openai = new OpenAI({
        apiKey: apiKey.trim(),
        fetch: (url, init) => fetch(url, { ...init, ...proxyConfig })
      });
  
      const response = await openai.chat.completions.create({
        model: "gpt-4.1-nano",
        messages: [{ role: 'user', content: 'Say this is a test!' }],
        response_format: { type: "text" },
        temperature: 1,
        max_tokens: 2048,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0
      });
  
      return {
        success: true,
        message: response.choices[0]?.message?.content || 'API 连接成功'
      }
  
    } catch (error) {
      console.error('Error testing API key:', error)
      return { 
        success: false,
        error: error instanceof Error ? error.message : '测试 API 密钥失败'
      }
    }
  }
  
  
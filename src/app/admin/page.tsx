// Removed getUserId import
import UsersTable from "./components/users-table";
import {
    getTotalUserCount,
    getTotalResumeCount,
    getTotalSubscriptionCount,
    getBaseResumeCount,
    getTailoredResumeCount,
    getProUserCount, // Import new action
    ensureAdmin // Import the new admin check function
} from "./actions";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, FileText, CreditCard, FileCheck, FilePlus, Star } from "lucide-react"; // Import Star, remove UsersRound

export default async function AdminPage() {
    try {
        // Ensure the current user is an admin, redirect if not
        await ensureAdmin();

        // Fetch all stats concurrently
        const [
            totalUsers,
            totalResumes,
            totalSubscriptions, // This might represent total *ever* subscriptions depending on the table
            baseResumes,
            tailoredResumes,
            proUsers // Fetch pro user count
        ] = await Promise.all([
            getTotalUserCount(),
            getTotalResumeCount(),
            getTotalSubscriptionCount(), // Keep or remove based on whether it's distinct from proUsers
            getBaseResumeCount(),
            getTailoredResumeCount(),
            getProUserCount() // Call the new action
        ]);

        // Removed averageResumesPerUser calculation

        return (
            <div className="container mx-auto py-10 px-4 md:px-6 lg:px-8">
                <div className="mb-8">
                    <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">管理员仪表板</h1>
                    <p className="text-muted-foreground mt-1">平台使用情况和用户管理概览。</p>
                </div>

                {/* Stats Section */}
                <section className="mb-10">
                    <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">平台统计</h2>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
                        {/* Total Users Card */}
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">
                                    总用户数
                                </CardTitle>
                                <Users className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{totalUsers.toLocaleString()}</div>
                            </CardContent>
                        </Card>

                        {/* Total Resumes Card */}
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">
                                    总简历数
                                </CardTitle>
                                <FileText className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{totalResumes.toLocaleString()}</div>
                            </CardContent>
                        </Card>

                        {/* Base Resumes Card */}
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">
                                    基础简历
                                </CardTitle>
                                <FileCheck className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{baseResumes.toLocaleString()}</div>
                            </CardContent>
                        </Card>

                        {/* Tailored Resumes Card */}
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">
                                    定制简历
                                </CardTitle>
                                <FilePlus className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{tailoredResumes.toLocaleString()}</div>
                            </CardContent>
                        </Card>

                        {/* Pro Users Card */}
                         <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">
                                    专业版用户
                                </CardTitle>
                                <Star className="h-4 w-4 text-muted-foreground" /> {/* Changed Icon */}
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{proUsers.toLocaleString()}</div> {/* Display pro users */}
                            </CardContent>
                        </Card>

                        {/* Total Subscriptions Card - Keep or remove? Depends if it's different from Pro Users */}
                        {/* If getTotalSubscriptionCount counts *all* subscriptions (active/inactive), keep it. */}
                        {/* If it's the same as getProUserCount, remove this card. */}
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">
                                    总订阅数
                                </CardTitle>
                                <CreditCard className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{totalSubscriptions.toLocaleString()}</div>
                            </CardContent>
                        </Card>
                    </div>
                </section>

                {/* User Management Section */}
                <section>
                    <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">用户管理</h2>
                    <UsersTable />
                </section>
            </div>
        );
    } catch (error) {
        console.error("Error during AdminPage server rendering:", error);
        // Fallback UI to display the error and prevent crashing the client context
        return (
            <div className="container mx-auto py-10 px-4 md:px-6 lg:px-8">
                <h1 className="text-3xl font-bold text-red-600">管理员页面服务器错误</h1>
                <p className="text-muted-foreground mt-1">在服务器上渲染管理员仪表板时发生意外错误。</p>
                <div className="mt-4 p-4 border border-red-300 bg-red-50 rounded-md">
                    <h2 className="text-lg font-semibold text-red-700">错误详情：</h2>
                    <pre className="mt-2 text-sm text-red-700 whitespace-pre-wrap">
                        {error instanceof Error ? error.message : String(error)}
                        {error instanceof Error && error.stack ? `\n\nStack Trace:\n${error.stack}` : ''}
                    </pre>
                </div>
                <p className="mt-4 text-sm text-gray-600">
                    请检查服务器日志以获取更多信息。页面的客户端部分可能无法正常运行。
                </p>
            </div>
        );
    }
}
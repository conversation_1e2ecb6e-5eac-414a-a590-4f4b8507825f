import { Background } from "@/components/landing/Background";
import FeatureHighlights from "@/components/landing/FeatureHighlights";
import { <PERSON> } from "@/components/landing/Hero";
import { PricingPlans } from "@/components/landing/PricingPlans";
import { VideoShowcase } from "@/components/landing/VideoShowcase";
import { CreatorStory } from "@/components/landing/creator-story";
import { FAQ } from "@/components/landing/FAQ";
import { Footer } from "@/components/layout/footer";
import { NavLinks } from "@/components/layout/nav-links";
import { Logo } from "@/components/ui/logo";
import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import { Metadata } from "next";
import Script from "next/script";

// Page-specific metadata that extends the base metadata from layout.tsx
export const metadata: Metadata = {
  title: "ZHIXIN - 技术职位 AI 简历构建器",
  description: "10分钟内创建 ATS 优化的技术简历。AI 智能定制让您的面试机会提升3倍。",
  openGraph: {
    title: "ZHIXIN - 技术职位 AI 简历构建器",
    description: "10分钟内创建 ATS 优化的技术简历。AI 智能定制让您的面试机会提升3倍。",
    url: "https://ZHIXIN.com",
  },
  twitter: {
    title: "ZHIXIN - 技术职位 AI 简历构建器",
    description: "10分钟内创建 ATS 优化的技术简历。AI 智能定制让您的面试机会提升3倍。",
  },
};

export default async function Page() {
  // Check if user is authenticated
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  
  // If user is authenticated, redirect to home page
  if (user) {
    redirect("/home");
  }
  
  return (
    <>
      {/* JSON-LD structured data for SEO */}
      <Script
        id="schema-data"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "SoftwareApplication",
            "name": "ZHIXIN",
            "applicationCategory": "BusinessApplication",
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD"
            },
            "description": "10分钟内创建 ATS 优化的技术简历。AI 智能定制让您的面试机会提升3倍。",
            "operatingSystem": "Web",
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": "4.8",
              "ratingCount": "500"
            }
          })
        }}
      />
    
      <main aria-label="ZHIXIN 首页" className=" ">
        {/* Simplified Navigation */}
        <nav aria-label="主导航" className="border-b border-gray-200 fixed top-0 w-full bg-white/95 z-[1000] transition-all duration-300 shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <Logo />
              <NavLinks />
            </div>
          </div>
        </nav>
        
        {/* Background component */}
        <Background />
        
        {/* Main content */}
        <div className="relative z-10 mx-auto px-4 sm:px-6 lg:px-24 flex flex-col justify-center">
          {/* Hero Section */}
          <Hero />
        </div>
        
        {/* Video Showcase Section */}
        <section id="product-demo">
          <VideoShowcase />
        </section>
        
        {/* Feature Highlights Section */}
        <section id="features" aria-labelledby="功能特色标题">
          <FeatureHighlights />
        </section>
        
        {/* Creator Story Section */}
        <section id="about" aria-labelledby="关于我们标题">
          <CreatorStory />
        </section>
        
        {/* Pricing Plans Section */}
        <section id="pricing" aria-labelledby="价格方案标题">
          <PricingPlans />
        </section>
        
        {/* FAQ Section */}
        <FAQ />

        <Footer variant="static"/>
      </main>
    </>
  );
}
@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
}

@layer base {
  :root {
    /* INS风格清新主题 - 浅色模式 */
    --background: 40 30% 98%;
    --foreground: 210 15% 20%;
    --card: 40 30% 98%;
    --card-foreground: 210 15% 20%;
    --popover: 40 30% 98%;
    --popover-foreground: 210 15% 20%;
    --primary: 160 40% 60%;
    --primary-foreground: 160 40% 98%;
    --secondary: 350 30% 90%;
    --secondary-foreground: 350 30% 30%;
    --muted: 30 10% 95%;
    --muted-foreground: 210 15% 40%;
    --accent: 20 80% 65%;
    --accent-foreground: 20 80% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 30 10% 90%;
    --input: 30 10% 90%;
    --ring: 160 40% 60%;
    --radius: 0.5rem;
  }

  .dark {
    /* INS风格清新主题 - 深色模式 */
    --background: 210 15% 20%;
    --foreground: 40 30% 98%;
    --card: 210 15% 20%;
    --card-foreground: 40 30% 98%;
    --popover: 210 15% 20%;
    --popover-foreground: 40 30% 98%;
    --primary: 160 30% 50%;
    --primary-foreground: 160 30% 98%;
    --secondary: 350 20% 40%;
    --secondary-foreground: 350 30% 98%;
    --muted: 210 15% 25%;
    --muted-foreground: 40 30% 80%;
    --accent: 20 50% 40%;
    --accent-foreground: 20 50% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 210 15% 25%;
    --input: 210 15% 25%;
    --ring: 160 30% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    background-image: radial-gradient(
      circle at top right,
      hsl(160 40% 95% / 0.3),
      transparent 50%
    ),
    radial-gradient(
      circle at bottom left,
      hsl(20 60% 95% / 0.3),
      transparent 50%
    ),
    radial-gradient(
      circle at center,
      hsl(350 30% 95% / 0.2),
      transparent 60%
    );
    background-attachment: fixed;
  }
}

@layer utilities {
  .glass-card {
    @apply bg-white/40 backdrop-blur-md border border-white/20 shadow-lg relative overflow-hidden;
  }
  
  .hover-card {
    @apply transition-all duration-500 hover:shadow-2xl hover:-translate-y-1 hover:bg-white/50;
  }

  @keyframes float {
    0% { transform: translate(0, 0) rotate(0deg); }
    50% { transform: translate(-10px, -10px) rotate(1deg); }
    100% { transform: translate(0, 0) rotate(0deg); }
  }

  @keyframes float-delayed {
    0% { transform: translate(0, 0) rotate(0deg); }
    50% { transform: translate(10px, -5px) rotate(-1deg); }
    100% { transform: translate(0, 0) rotate(0deg); }
  }

  .animate-float {
    animation: float 8s ease-in-out infinite;
  }

  .animate-float-delayed {
    animation: float-delayed 9s ease-in-out infinite;
  }

  .bg-radial-glow-mint {
    background: radial-gradient(
      circle at center,
      theme('colors.ins-mint.500/0.15') 0%,
      theme('colors.ins-mint.500/0.05') 45%,
      transparent 70%
    );
  }

  .bg-radial-glow-coral {
    background: radial-gradient(
      circle at center,
      theme('colors.ins-coral.500/0.15') 0%,
      theme('colors.ins-coral.500/0.05') 45%,
      transparent 70%
    );
  }
}

.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #e2e8f0 transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #e2e8f0;
  border-radius: 20px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #cbd5e1;
}



.buyButton {
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  color: #ffffff;
  background-color: #ff813f;
  border-radius: 5px;
  border: 1px solid transparent;
  padding: 0.5rem;
  font-size: 1rem;
  letter-spacing: 0.6px;
  box-shadow: 0px 1px 2px rgba(190, 190, 190, 0.5);
  font-family: cursive;
}
.buyButton:hover,
.buyButton:active,
.buyButton:focus {
  text-decoration: underline;
  box-shadow: 0px 1px 2px 2px rgba(190, 190, 190, 0.5);
  opacity: 0.85;
  color: #ffffff;
}

.coffeeImage {
  height: 2vh;
  box-shadow: none;
  border: none;
  vertical-align: middle;
}

.coffeeButtonText {
  margin-left: 15px;
  font-size: 0.8rem;
  vertical-align: middle;
}

/* Buy Me Coffee Button Styles */
#bmc-wbtn {
  position: fixed !important;
  bottom: 1.5rem !important;
  right: 1.5rem !important;
  z-index: 50 !important;
  transition: transform 0.2s ease-in-out !important;
}

#bmc-wbtn:hover {
  transform: translateY(-2px) !important;
}

@media (max-width: 640px) {
  #bmc-wbtn {
    bottom: 1rem !important;
    right: 1rem !important;
    transform: scale(0.9) !important;
  }
}
/* 
h1 {
  display: block;
  font-size: 2em;
  margin-top: 0.67em;
  margin-bottom: 0.67em;
  margin-left: 0;
  margin-right: 0;
  font-weight: bold;
}


h2 {
  display: block;
  font-size: 1.5em;
  margin-top: 0.83em;
  margin-bottom: 0.83em;
  margin-left: 0;
  margin-right: 0;
  font-weight: bold;
}

h3 {
  display: block;
  font-size: 1.17em;
  margin-top: 1em;
  margin-bottom: 1em;
  margin-left: 0;
  margin-right: 0;
  font-weight: bold;
}

h4 {
  display: block;
  font-size: 1em;
  margin-top: 1.33em;
  margin-bottom: 1.33em;
  margin-left: 0;
  margin-right: 0;
  font-weight: bold;
}

h5 {
  display: block;
  font-size: .83em;
  margin-top: 1.67em;
  margin-bottom: 1.67em;
  margin-left: 0;
  margin-right: 0;
  font-weight: bold;
}

h6 {
  display: block;
  font-size: .67em;
  margin-top: 2.33em;
  margin-bottom: 2.33em;
  margin-left: 0;
  margin-right: 0;
  font-weight: bold;
} */